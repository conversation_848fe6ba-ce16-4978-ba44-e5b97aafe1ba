const { Connection, Request, TYPES } = require("tedious");
const CONFIG = require("../config");

module.exports = {
  executeQuery,
  executeTransaction,
  executeQueryStoreLogisticsManager,
};

function executeQuery(query, params = {}) {
  return new Promise((resolve, reject) => {
    const connection = new Connection(CONFIG.dbConfig);
    connection.on("connect", (err) => {
      if (err) {
        console.error("Connection error", err);
        return reject(err);
      } else {
        const request = new Request(query, (err, rowCount, rows) => {
          connection.close();
          if (err) {
            console.error("Request error", err);
            return reject(err);
          } else {
            const result = rows.map((row) => {
              const rowObject = {};
              row.forEach((column) => {
                rowObject[column.metadata.colName] = column.value;
              });
              return rowObject;
            });
            return resolve(result);
          }
        });

        if (params && Object.keys(params).length > 0) {
          Object.keys(params).forEach((key) => {
            const value = params[key];
            let type = TYPES.NVarChar;

            if (typeof value === "number") {
              if (Number.isInteger(value)) {
                type = TYPES.Int;
              } else {
                type = TYPES.Float;
              }
            } else if (typeof value === "boolean") {
              type = TYPES.Bit;
            } else if (value instanceof Date) {
              type = TYPES.DateTime;
            }

            request.addParameter(key, type, value);
          });
        }

        connection.execSql(request);
      }
    });
    connection.connect();
  });
}

function executeQueryStoreLogisticsManager(query, params = {}) {
  return new Promise((resolve, reject) => {
    const connection = new Connection(CONFIG.dbConfigEms);
    connection.on("connect", (err) => {
      if (err) {
        console.error("Connection error", err);
        return reject(err);
      } else {
        const request = new Request(query, (err, rowCount, rows) => {
          connection.close();
          if (err) {
            console.error("Request error", err);
            return reject(err);
          } else {
            const result = rows.map((row) => {
              const rowObject = {};
              row.forEach((column) => {
                rowObject[column.metadata.colName] = column.value;
              });
              return rowObject;
            });
            return resolve(result);
          }
        });

        if (params && Object.keys(params).length > 0) {
          Object.keys(params).forEach((key) => {
            const value = params[key];
            let type = TYPES.NVarChar;

            if (typeof value === "number") {
              if (Number.isInteger(value)) {
                type = TYPES.Int;
              } else {
                type = TYPES.Float;
              }
            } else if (typeof value === "boolean") {
              type = TYPES.Bit;
            } else if (value instanceof Date) {
              type = TYPES.DateTime;
            }

            request.addParameter(key, type, value);
          });
        }

        connection.execSql(request);
      }
    });
    connection.connect();
  });
}

async function executeTransaction(queries, useEmsDb = true) {
  return new Promise((resolve, reject) => {
    const connection = new Connection(useEmsDb ? CONFIG.dbConfigEms : CONFIG.dbConfig);
    const results = [];

    connection.on("connect", (err) => {
      if (err) {
        console.error("Connection error", err);
        return reject(err);
      }

      connection.beginTransaction((err) => {
        if (err) {
          console.error("Begin transaction error", err);
          connection.close();
          return reject(err);
        }

        const executeQueries = (index) => {
          if (index >= queries.length) {
            connection.commitTransaction((err) => {
              if (err) {
                console.error("Commit transaction error", err);
                connection.rollbackTransaction((rollbackErr) => {
                  connection.close();
                  if (rollbackErr) {
                    console.error("Rollback transaction error", rollbackErr);
                  }
                  reject(err);
                });
              } else {
                connection.close();
                resolve(results);
              }
            });
          } else {
            const { query, params } = queries[index];
            const request = new Request(query, (err, rowCount) => {
              if (err) {
                console.error("Query execution error", err);
                connection.rollbackTransaction((rollbackErr) => {
                  connection.close();
                  if (rollbackErr) {
                    console.error("Rollback transaction error", rollbackErr);
                  }
                  reject(err);
                });
              } else {
                results.push({ rowCount });
                executeQueries(index + 1);
              }
            });

            for (const [key, value] of Object.entries(params)) {
              if (value === null) {
                request.addParameter(key, TYPES.NVarChar, null);
              } else if (typeof value === "number") {
                if (Number.isInteger(value)) {
                  request.addParameter(key, TYPES.Int, value);
                } else {
                  request.addParameter(key, TYPES.Float, value);
                }
              } else if (value instanceof Date) {
                request.addParameter(key, TYPES.DateTime, value);
              } else {
                request.addParameter(key, TYPES.NVarChar, value.toString());
              }
            }

            connection.execSql(request);
          }
        };

        executeQueries(0);
      });
    });

    connection.connect();
  });
}
